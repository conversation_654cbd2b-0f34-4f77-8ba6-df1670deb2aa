import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LcaDataSubmission,
  VendorCode,
} from '../models';
import {VendorCodeRepository} from '../repositories';

export class VendorCodeLcaDataSubmissionController {
  constructor(
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
  ) { }

  @get('/vendor-codes/{id}/lca-data-submissions', {
    responses: {
      '200': {
        description: 'Array of VendorCode has many LcaDataSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LcaDataSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<LcaDataSubmission>,
  ): Promise<LcaDataSubmission[]> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).find(filter);
  }

  @post('/vendor-codes/{id}/lca-data-submissions', {
    responses: {
      '200': {
        description: 'VendorCode model instance',
        content: {'application/json': {schema: getModelSchemaRef(LcaDataSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof VendorCode.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LcaDataSubmission, {
            title: 'NewLcaDataSubmissionInVendorCode',
            exclude: ['id'],
            optional: ['vendorId']
          }),
        },
      },
    }) lcaDataSubmission: Omit<LcaDataSubmission, 'id'>,
  ): Promise<LcaDataSubmission> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).create(lcaDataSubmission);
  }

  @patch('/vendor-codes/{id}/lca-data-submissions', {
    responses: {
      '200': {
        description: 'VendorCode.LcaDataSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LcaDataSubmission, {partial: true}),
        },
      },
    })
    lcaDataSubmission: Partial<LcaDataSubmission>,
    @param.query.object('where', getWhereSchemaFor(LcaDataSubmission)) where?: Where<LcaDataSubmission>,
  ): Promise<Count> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).patch(lcaDataSubmission, where);
  }

  @del('/vendor-codes/{id}/lca-data-submissions', {
    responses: {
      '200': {
        description: 'VendorCode.LcaDataSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(LcaDataSubmission)) where?: Where<LcaDataSubmission>,
  ): Promise<Count> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).delete(where);
  }

  @get('/vendor-codes/{id}/lca-data-submissions/count', {
    responses: {
      '200': {
        description: 'Number of LcaDataSubmission records for VendorCode',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(LcaDataSubmission)) where?: Where<LcaDataSubmission>,
  ): Promise<Count> {
    const submissions = await this.vendorCodeRepository.lcaDataSubmissions(id).find({where});
    return {count: submissions.length};
  }

  @get('/vendor-codes/{id}/lca-data-submissions/latest', {
    responses: {
      '200': {
        description: 'Latest LcaDataSubmission for VendorCode',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LcaDataSubmission),
          },
        },
      },
    },
  })
  async findLatest(
    @param.path.number('id') id: number,
  ): Promise<LcaDataSubmission | null> {
    const submissions = await this.vendorCodeRepository.lcaDataSubmissions(id).find({
      order: ['created_on DESC'],
      limit: 1,
    });
    return submissions.length > 0 ? submissions[0] : null;
  }

  @get('/vendor-codes/{id}/lca-data-submissions/by-part-number/{partNumber}', {
    responses: {
      '200': {
        description: 'Array of LcaDataSubmission for specific part number',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LcaDataSubmission)},
          },
        },
      },
    },
  })
  async findByPartNumber(
    @param.path.number('id') id: number,
    @param.path.string('partNumber') partNumber: string,
  ): Promise<LcaDataSubmission[]> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).find({
      where: {
        partNumber: partNumber,
      },
      order: ['created_on DESC'],
    });
  }

  @get('/vendor-codes/{id}/lca-data-submissions/approved', {
    responses: {
      '200': {
        description: 'Array of approved LcaDataSubmission for VendorCode',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LcaDataSubmission)},
          },
        },
      },
    },
  })
  async findApproved(
    @param.path.number('id') id: number,
  ): Promise<LcaDataSubmission[]> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).find({
      where: {
        and: [
          {approved_by: {neq: null}},
          {approved_on: {neq: null}},
        ],
      },
      order: ['approved_on DESC'],
    });
  }

  @get('/vendor-codes/{id}/lca-data-submissions/pending', {
    responses: {
      '200': {
        description: 'Array of pending LcaDataSubmission for VendorCode',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LcaDataSubmission)},
          },
        },
      },
    },
  })
  async findPending(
    @param.path.number('id') id: number,
  ): Promise<LcaDataSubmission[]> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).find({
      where: {
        or: [
          {approved_by: null},
          {approved_on: null},
        ],
      },
      order: ['created_on DESC'],
    });
  }

  @get('/vendor-codes/{id}/lca-data-submissions/rejected', {
    responses: {
      '200': {
        description: 'Array of rejected LcaDataSubmission for VendorCode',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LcaDataSubmission)},
          },
        },
      },
    },
  })
  async findRejected(
    @param.path.number('id') id: number,
  ): Promise<LcaDataSubmission[]> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).find({
      where: {
        reject: {neq: null},
      },
      order: ['created_on DESC'],
    });
  }

  @get('/vendor-codes/{id}/lca-data-submissions/with-vendor-details', {
    responses: {
      '200': {
        description: 'Array of LcaDataSubmission with vendor details',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LcaDataSubmission, {includeRelations: true})},
          },
        },
      },
    },
  })
  async findWithVendorDetails(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<LcaDataSubmission>,
  ): Promise<LcaDataSubmission[]> {
    return this.vendorCodeRepository.lcaDataSubmissions(id).find({
      ...filter,
      include: [
        {
          relation: 'vendor',
          scope: {
            fields: ['id', 'code', 'supplierName', 'supplierSPOC', 'supplierContact'],
          },
        },
      ],
    });
  }

  @get('/vendor-codes/{id}/lca-data-submissions/summary', {
    responses: {
      '200': {
        description: 'Summary of LcaDataSubmission for VendorCode',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                total: {type: 'number'},
                approved: {type: 'number'},
                pending: {type: 'number'},
                rejected: {type: 'number'},
                latest: {type: 'object'},
              },
            },
          },
        },
      },
    },
  })
  async getSummary(
    @param.path.number('id') id: number,
  ): Promise<{
    total: number;
    approved: number;
    pending: number;
    rejected: number;
    latest: LcaDataSubmission | null;
  }> {
    const [allSubmissions, latest] = await Promise.all([
      this.vendorCodeRepository.lcaDataSubmissions(id).find(),
      this.findLatest(id),
    ]);

    const total = allSubmissions.length;
    const approved = allSubmissions.filter(
      submission => submission.approved_by != null && submission.approved_on != null
    ).length;
    const pending = allSubmissions.filter(
      submission => submission.approved_by == null || submission.approved_on == null
    ).length;
    const rejected = allSubmissions.filter(
      submission => submission.reject != null
    ).length;

    return {
      total,
      approved,
      pending,
      rejected,
      latest,
    };
  }
}
